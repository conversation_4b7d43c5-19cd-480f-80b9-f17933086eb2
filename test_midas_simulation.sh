#!/bin/bash

# Test script for Midas SITL simulation
# This script will test if the Midas model can be launched properly

echo "Testing Midas SITL simulation setup..."

# Check if the airframe file exists
if [ -f "ROMFS/px4fmu_common/init.d-posix/airframes/22000_gz_midas" ]; then
    echo "✓ Airframe configuration file exists"
else
    echo "✗ Airframe configuration file missing"
    exit 1
fi

# Check if the model directory exists
if [ -d "Tools/simulation/gz/models/midas" ]; then
    echo "✓ Model directory exists"
else
    echo "✗ Model directory missing"
    exit 1
fi

# Check if required mesh files exist
MESH_FILES=(
    "Tools/simulation/gz/models/midas/meshes/body_compound.dae"
    "Tools/simulation/gz/models/midas/meshes/arms_compound.dae"
    "Tools/simulation/gz/models/midas/meshes/prop_compound_1.dae"
    "Tools/simulation/gz/models/midas/meshes/tilt_assembly_3.dae"
    "Tools/simulation/gz/models/midas/meshes/tilt_assembly_4.dae"
)

for mesh_file in "${MESH_FILES[@]}"; do
    if [ -f "$mesh_file" ]; then
        echo "✓ Mesh file exists: $(basename $mesh_file)"
    else
        echo "✗ Mesh file missing: $(basename $mesh_file)"
        exit 1
    fi
done

# Check if model.sdf and model.config exist
if [ -f "Tools/simulation/gz/models/midas/model.sdf" ]; then
    echo "✓ Model SDF file exists"
else
    echo "✗ Model SDF file missing"
    exit 1
fi

if [ -f "Tools/simulation/gz/models/midas/model.config" ]; then
    echo "✓ Model config file exists"
else
    echo "✗ Model config file missing"
    exit 1
fi

# Check if the airframe is registered in CMakeLists.txt
if grep -q "22000_gz_midas" "ROMFS/px4fmu_common/init.d-posix/airframes/CMakeLists.txt"; then
    echo "✓ Airframe is registered in CMakeLists.txt"
else
    echo "✗ Airframe not registered in CMakeLists.txt"
    exit 1
fi

echo ""
echo "All checks passed! ✓"
echo ""
echo "To test the simulation, run:"
echo "make px4_sitl gz_midas"
echo ""
echo "Or with a specific world:"
echo "PX4_GZ_WORLD=default make px4_sitl gz_midas"
